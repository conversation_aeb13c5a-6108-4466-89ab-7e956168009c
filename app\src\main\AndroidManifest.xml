<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.github.kr328.ibr">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:ignore="GoogleAppIndexingWarning">
        <activity android:name=".MainActivity" android:label="@string/app_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".AppEditActivity" android:label="@string/edit_app_application_title" android:exported="false"/>
        <activity android:name=".RuleViewerActivity" android:exported="false" />
        <activity android:name=".SettingsActivity" android:label="@string/setting_title" android:exported="false" />
        <activity android:name=".AboutActivity" android:label="@string/about_activity_title" android:exported="false" />
        <activity android:name=".FirstInstallActivity" android:exported="true"
            android:theme="@android:style/Theme.NoDisplay" />
    </application>
</manifest>
