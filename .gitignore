.gradle
build/
/app/release/

# Ignore Gradle GUI config
gradle-app.setting

# Avoid ignoring Gradle wrapper jar targetFile (.jar files are usually ignored)
!gradle-wrapper.jar

# Cache of project
.gradletasknamecache

# # Work around https://youtrack.jetbrains.com/issue/IDEA-116898
# gradle/wrapper/gradle-wrapper.properties

# Ignore IDEA config
.idea
*.iml

# KeyStore
*.keystore
*.jks

# gradle
.gradle

# clion cmake build
cmake-build-*

# local.properties
local.properties

# keystore
keystore.properties

# vscode
.vscode

# cxx
.cxx

*.hprof
