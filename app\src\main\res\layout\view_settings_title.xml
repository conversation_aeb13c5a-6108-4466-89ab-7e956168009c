<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:elevation="0dp"
        android:background="#EEEEEE"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/view_settings_title_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/colorAccent"
        android:paddingStart="80dp"
        android:paddingEnd="15dp"
        android:paddingTop="15dp"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
