<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.github.kr328.ibr.view.SettingAppInfo
            android:id="@+id/activity_rule_viewer_app_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.github.kr328.ibr.view.SettingTitle
            custom:title="@string/rule_viewer_activity_title_online"
            android:id="@+id/activity_rule_viewer_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/activity_rule_viewer_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.github.kr328.ibr.view.SettingButton
            custom:icon="@drawable/ic_add"
            custom:title="@string/rule_viewer_activity_new_rule"
            android:id="@+id/activity_rule_viewer_add_rule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>
</ScrollView>