buildscript {
    ext.kotlin_version = '1.6.10'

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
    ext {
        moduleId = "ibr"
        versionCode = 1
        versionName = "1.0"
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
