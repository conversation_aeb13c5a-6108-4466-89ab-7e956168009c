<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/adapter_rule_item_clickable"
    android:clickable="true"
    android:focusable="true"
    android:paddingTop="15dp"
    android:paddingBottom="15dp"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground">

    <TextView
        android:id="@+id/adapter_rule_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginStart="70dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/adapter_rule_item_summary"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/adapter_rule_item_summary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        app:layout_constraintStart_toStartOf="@id/adapter_rule_item_title"
        app:layout_constraintTop_toBottomOf="@id/adapter_rule_item_title"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
