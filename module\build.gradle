import org.apache.tools.ant.filters.FixCrLfFilter
import org.gradle.internal.os.OperatingSystem

apply plugin: 'com.android.application'

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"
}
ext {
    is_windows = OperatingSystem.current().isWindows()
    zipFileName = "zygisk-internal-browser-redirect.zip"
}

task magisk(type: Zip) {
    from(project(":remote").file("build/outputs/remote-library")) {
        include("classes.dex")
        rename("classes.dex", "ibr.dex")
        into "framework/"
    }
    from(project(":remote").fileTree("build/outputs/remote-library/armeabi-v7a")) {
        include "*.so"
        rename(".*\\.so", "armeabi-v7a.so")
        into "zygisk"
    }
    from(project(":remote").fileTree("build/outputs/remote-library/arm64-v8a")) {
        include "*.so"
        rename(".*\\.so", "arm64-v8a.so")
        into "zygisk"
    }
    from(project(":remote").fileTree("build/outputs/remote-library/x86")) {
        include "*.so"
        rename(".*\\.so", "x86.so")
        into "zygisk"
    }
    from(project(":remote").fileTree("build/outputs/remote-library/x86_64")) {
        include "*.so"
        rename(".*\\.so", "x86_64.so")
        into "zygisk"
    }
    from(project(":app").file("build/intermediates/apk/release/app-release.apk")) {
        rename {
            "app.apk"
        }
    }
    from(file("src/main/raw/magisk"))

    destinationDirectory = file("$buildDir/outputs")
    archiveFileName = "$zipFileName"

    outputs.upToDateWhen { false }
}

task push(type: Exec) {
    dependsOn magisk
    def zipFile = "$buildDir/outputs/$zipFileName"
    def commands = [android.adbExecutable, "push",
                    zipFile,
                    "/data/local/tmp/"]
    if (is_windows) {
        commandLine 'cmd', '/c', commands.join(" ")
    } else {
        commandLine commands
    }
}

task flash(type: Exec) {
    dependsOn push
    def commands = [android.adbExecutable, "shell", "su", "-c",
                    "magisk --install-module /data/local/tmp/$zipFileName"]
    if (is_windows) {
        commandLine 'cmd', '/c', commands.join(" ")
    } else {
        commandLine commands
    }
}

task flashAndReboot(type: Exec) {
    dependsOn flash
    def commands = [android.adbExecutable, "reboot"]
    if (is_windows) {
        commandLine 'cmd', '/c', commands.join(" ")
    } else {
        commandLine commands
    }
}

gradle.projectsEvaluated {
    magisk.dependsOn(project(":remote").tasks.getByName("extractRemoteLibrary"))
    magisk.dependsOn(project(":app").tasks.getByName("assembleRelease"))
}
