apply plugin: 'com.android.application'

android {
    compileSdkVersion 32
    buildToolsVersion "32.0.0"
    ndkVersion "23.1.7779620"

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion 32
        versionCode versionCode
        versionName versionName

        externalNativeBuild {
            cmake {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
                var configFlags = "-Oz -DNDEBUG"
                arguments "-DMODULE_NAME:STRING=$moduleId",
                        "-DANDROID_STL=none",
                        "-DCMAKE_CXX_FLAGS_RELEASE=$configFlags",
                        "-DCMAKE_CXX_FLAGS_RELWITHDEBINFO=$configFlags",
                        "-DCMAKE_C_FLAGS_RELEASE=$configFlags",
                        "-DCMAKE_C_FLAGS_RELWITHDEBINFO=$configFlags"

                cppFlags "-std=c++20",
                        "-ffixed-x18",
                        "-Qunused-arguments",
                        "-fno-rtti", "-fno-exceptions",
                        "-fno-stack-protector",
                        "-fomit-frame-pointer",
                        "-Wno-builtin-macro-redefined",
                        "-Wl,--exclude-libs,ALL",
                        "-D__FILE__=__FILE_NAME__",
                        "-Wl,--strip-all"
            }
        }
    }

    buildFeatures {
        prefab true
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation 'dev.rikka.ndk.thirdparty:cxx:1.2.0'
    implementation project(":shared")
    compileOnly project(":hideapi")
}

task extractRemoteLibrary(type: Copy) {
    from zipTree("$buildDir/intermediates/apk/release/remote-release-unsigned.apk")
    include "lib/**"
    include "*.dex"
    into "$buildDir/outputs/remote-library/"

    eachFile {
        path = path.replaceAll("^lib/", "")
    }
    outputs.upToDateWhen { false }
}

afterEvaluate {
    extractRemoteLibrary.dependsOn assembleRelease
}
