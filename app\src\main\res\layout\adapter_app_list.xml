<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:clickable="true"
    android:background="?selectableItemBackground"
    android:padding="13dp">

    <ImageView
        android:id="@+id/adapter_app_list_icon"
        android:layout_width="45dp"
        android:layout_height="45dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/app_name" />

    <TextView
        android:id="@+id/adapter_app_list_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:singleLine="true"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/adapter_app_list_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/adapter_app_list_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/adapter_app_list_name" />
</androidx.constraintlayout.widget.ConstraintLayout>
