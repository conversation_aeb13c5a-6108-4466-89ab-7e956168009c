apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlinx-serialization'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion 32
    buildToolsVersion '32.0.0'

    kotlinOptions {
        jvmTarget = '11'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        applicationId "com.github.kr328.ibr"
        minSdkVersion 26
        targetSdkVersion 32
        versionCode 12000
        versionName "12.0"
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    kapt 'androidx.room:room-compiler:2.4.1'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlin_version}"
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-runtime:1.0-M1-1.4.0-rc'
    implementation 'androidx.room:room-runtime:2.4.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.4.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.4.0'
    implementation 'androidx.browser:browser:1.4.0'
    implementation 'androidx.preference:preference-ktx:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.fragment:fragment-ktx:1.4.1'
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    implementation project(":shared")
}
