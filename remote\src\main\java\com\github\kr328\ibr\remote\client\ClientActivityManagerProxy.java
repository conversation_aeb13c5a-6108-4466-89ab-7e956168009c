package com.github.kr328.ibr.remote.client;

import android.content.Intent;
import android.util.Log;

import com.github.kr328.ibr.remote.Constants;
import com.github.kr328.ibr.remote.shared.RuleSet;

public class ClientActivityManagerProxy extends BaseClientActivityManagerProxy {
    private final RuleSetCache cache = new RuleSetCache();

    @Override
    protected void handleStartActivity(StartActivityPayloads payloads) {
        if (payloads.intent.getComponent() == null)
            return;

        if (payloads.intent.hasCategory(Constants.INTENT_CATEGORY_IGNORE))
            return;

        try {
            RuleSet ruleSet = cache.getRuleSet(payloads.callingPackage);

            if (ruleSet == null)
                return;

            if (ruleSet.debug) {
                for (String line : Logger.log(payloads.callingPackage, payloads.intent).split("\n"))
                    Log.d(Constants.TAG, line);
            }

            RuleSetMatcher.Result result = RuleSetMatcher.matches(ruleSet, payloads.intent);

            if (result != null) {
                Log.i(Constants.TAG, "Rule " + result.ruleSetTag + "|" + result.ruleTag + " matches " + result.uri);

                // 直接重定向到外部浏览器，不显示选择器
                Intent redirectIntent = new Intent(Intent.ACTION_VIEW).setData(result.uri);

                payloads.intent = redirectIntent;
                payloads.options = null;
            }
        } catch (Exception e) {
            Log.w(Constants.TAG, "Query RuleSet failure", e);
        }
    }
}
