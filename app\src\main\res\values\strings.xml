<resources>
    <string name="app_name">InternalBrowserRedirect</string>

    <string name="app_list_application_error_invalid_service_button_ok">OK</string>
    <string name="app_list_application_error_update_failure">Online Rule Sync Failure</string>
    <string name="app_list_application_error_empty_app_list">No any app supported on this device</string>
    <string name="app_list_application_error_invalid_service_title">Redirect Service Invalid</string>
    <string name="app_list_application_error_invalid_service_message_unknown">
        Everything seems work, but it still unable to connect to the service.
    </string>
    <string name="app_list_application_error_invalid_service_message_riru_not_load">
        Riru - Core did not load Riru - InternalBrowserRedirect \n
        Please check follow module has been installed in magisk \n
        - Riru - Core \n
        - Riru - InternalBrowserRedirect \n
    </string>
    <string name="app_list_application_error_invalid_service_message_not_call_fork">
        Riru - Core and Riru - InternalBrowserRedirect loaded, but <PERSON><PERSON><PERSON> <PERSON> Core unable to inject system_server \n
        It seem that some magisk module incompatible with Riru - Core
    </string>
    <string name="app_list_application_error_invalid_service_message_inject_failure">
        Riru - Core and Riru - InternalBrowserRedirect have injected to system_server, but some process failed
    </string>
    <string name="app_list_application_error_invalid_service_message_service_not_created">
        Riru - Core and Riru - InternalBrowserRedirect inject to system_server, but some process failed \n
        It seem that some module incompatible with Riru - InternalBrowserRedirect
    </string>
    <string name="app_list_application_error_invalid_service_message_service_unable_to_handle">
        Riru - Core and Riru - InternalBrowserRedirect inject to system_server, but some process failed \n
        It seem that some module incompatible with Riru - InternalBrowserRedirect
    </string>
    <string name="app_list_application_error_invalid_service_message_system_block_ipc">
        Everything is working in service side, but we unable to communicate with service \n
        It seem that system block IPC
    </string>
    <string name="app_list_application_error_invalid_service_message_service_version_not_matches">
        Redirect service version not match controller version \n
        Please check Riru - InternalBrowserRedirect and this app verion
    </string>

    <string name="app_list_application_state_enabled">Enabled (%d Rules)</string>
    <string name="app_list_application_state_disabled">Disabled (%d Rules)</string>

    <string name="app_list_application_menu_rule_set">New RuleSet</string>
    <string name="app_list_application_menu_settings">Settings</string>
    <string name="app_list_application_menu_help">Help</string>
    <string name="app_list_application_about">About</string>

    <string name="app_list_application_query_data_failure">Query Data Failure</string>
    <string name="app_list_application_refresh_failure">Refresh Failure</string>

    <string name="edit_app_application_title">Application Preference</string>
    <string name="edit_app_application_enable">Enable</string>

    <string name="edit_app_application_menu_remove_local_rule_set">Remove Local RuleSet</string>

    <string name="edit_app_application_develop">Develop</string>
    <string name="edit_app_application_debug_mode">Debug Mode</string>
    <string name="edit_app_application_debug_mode_summary">Print all intent to log [TAG: InternalBrowserRedirect]</string>
    <string name="edit_app_application_online_rule_set">Online Rule Set</string>
    <string name="edit_app_application_online_rule_set_enable">Enable</string>
    <string name="edit_app_application_online_rule_set_enable_summary">Enable online rule set</string>
    <string name="edit_app_application_online_rule_set_tag">TAG</string>
    <string name="edit_app_application_online_rule_set_author">Authors</string>
    <string name="edit_app_application_online_rule_set_last_update">Last Update</string>
    <string name="edit_app_application_online_rule_set_view_rules">View Rules</string>
    <string name="edit_app_application_online_rule_set_view_rules_summary">%d Rules</string>
    <string name="edit_app_application_local_rule_set">Local Rule Set</string>
    <string name="edit_app_application_local_rule_set_enable">Enable</string>
    <string name="edit_app_application_local_rule_set_enable_summary">Enable local rule set</string>
    <string name="edit_app_application_local_rule_set_view_rules">View Rules</string>
    <string name="edit_app_application_local_rule_set_view_rules_summary">%d Rules</string>

    <string name="edit_app_application_rule_set_unknown">Unknown</string>
    <string name="edit_app_application_rule">Rule</string>

    <string name="edit_app_application_exception_load_app_info_failure">Load App Info Failure</string>
    <string name="edit_app_application_exception_query_data_failure">Query From Service Failure</string>
    <string name="edit_app_application_exception_refresh_failure">Refresh Failure</string>
    <string name="edit_app_application_exception_push_data_to_service_failure">Push Data to Service Failure</string>

    <string name="rule_viewer_activity_title_edit">Edit Rules</string>
    <string name="rule_viewer_activity_title_view">View Rules</string>
    <string name="rule_viewer_activity_title_online">Online Rules</string>
    <string name="rule_viewer_activity_title_local">Local Rules</string>
    <string name="rule_viewer_activity_new_rule">New Rule</string>

    <string name="rule_viewer_dialog_title_local">Edit Rule</string>
    <string name="rule_viewer_dialog_title_online">View Rule</string>
    <string name="rule_viewer_dialog_tag">Tag</string>
    <string name="rule_viewer_dialog_url_source">URL Source</string>
    <string name="rule_viewer_dialog_regex_ignore">Ignore URL Regex</string>
    <string name="rule_viewer_dialog_regex_force">Force URL Regex</string>
    <string name="rule_viewer_dialog_button_ok">OK</string>
    <string name="rule_viewer_dialog_button_cancel">Cancel</string>
    <string name="rule_viewer_dialog_button_delete">Delete</string>
    <string name="rule_viewer_dialog_toast_invalid_tag">Invalid Tag</string>
    <string name="rule_viewer_dialog_toast_invalid_url_source">Invalid url source</string>
    <string name="rule_viewer_dialog_toast_invalid_ignore_regex">Invalid Ignore Regex</string>
    <string name="rule_viewer_dialog_toast_invalid_force_regex">Invalid Force Regex</string>

    <string name="setting_title">Settings</string>
    <string name="setting_online_rule">Online Rule</string>
    <string name="setting_online_rule_user">Github User</string>
    <string name="setting_online_rule_repo">Github Repo</string>
    <string name="setting_online_rule_branch">Repo Branch</string>

    <string name="first_install_notification_channel">Install Notification</string>
    <string name="first_install_notification_success_installed_title">InternalBrowserRedirect Installed</string>
    <string name="first_install_notification_failure_installed_title">InternalBrowserRedirect Install Failed</string>
    <string name="first_install_notification_success_installed_message">Click here to configure</string>
    <string name="first_install_notification_failure_installed_message">Click here for details</string>

    <string name="about_activity_title">About</string>
    <string name="about_activity_developer">Developer</string>
    <string name="about_activity_developer_value" translatable="false">Kr328</string>
    <string name="about_activity_telegram_grouo">Telegram Group</string>
    <string name="about_activity_telegram_group_value" translatable="false">https://t.me/kr328_riru_modules</string>
    <string name="about_activity_source_code">Source Code</string>
    <string name="about_activity_source_code_value" translatable="false">https://github.com/Kr328/Riru-InternalBrowserRedirect</string>
    <string name="about_activity_rules_repo">Rules Repo</string>
    <string name="about_activity_rules_repo_value" translatable="false">https://github.com/Kr328/Riru-InternalBrowserRedirect-Rules</string>
    <string name="about_activity_donate">Donate</string>
    <string name="about_activity_donate_summary">Thanks for your support</string>
</resources>
